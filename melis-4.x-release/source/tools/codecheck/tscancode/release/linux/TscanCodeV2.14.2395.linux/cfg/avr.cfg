<?xml version="1.0"?>
<!-- Based on http://www.nongnu.org/avr-libc/user-manual/group__avr__stdint.html -->
<def format="1">
  <!-- stdint.h -->
  <define name="__USING_MINT8" value="0"/>
  <define name="__CONCATenate(left, right)" value="left ## right"/>
  <define name="__CONCAT(left, right)" value="__CONCATenate(left, right)"/>
  <!-- Integer types capable of holding object pointers -->
  <podtype name="intptr_t" sign="s" size="2"/>
  <podtype name="uintptr_t" sign="u" size="2"/>
  <!-- Minimum-width integer types -->
  <podtype name="int_least8_t" sign="s" size="1"/>
  <podtype name="uint_least8_t" sign="u" size="1"/>
  <podtype name="int_least16_t" sign="s" size="2"/>
  <podtype name="uint_least16_t" sign="u" size="2"/>
  <podtype name="int_least32_t" sign="s" size="4"/>
  <podtype name="uint_least32_t" sign="u" size="4"/>
  <podtype name="int_least64_t" sign="s" size="8"/>
  <podtype name="uint_least64_t" sign="u" size="8"/>
  <!-- Greatest-width integer types -->
  <podtype name="intmax_t" sign="s" size="8"/>
  <podtype name="uintmax_t" sign="u" size="8"/>
  <!-- ctype.h -->
  <function name="toascii">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <valid>0:255</valid>
    </arg>
  </function>
  <!-- errno.h -->
  <define name="EDOM" value="33"/>
  <define name="ERANGE" value="34"/>
  <!-- inttypes.h -->
  <podtype name="int_farptr_t" sign="s" size="4"/>
  <podtype name="uint_farptr_t" sign="u" size="4"/>
  <define name="INT8_MAX" value="0x7f"/>
  <define name="INT8_MIN" value="(-INT8_MAX - 1)"/>
  <define name="UINT8_MAX" value="(__CONCAT(INT8_MAX, U) * 2U + 1U)"/>
  <define name="INT16_MAX" value="0x7fff"/>
  <define name="INT16_MIN" value="(-INT16_MAX - 1)"/>
  <define name="UINT16_MAX" value="(__CONCAT(INT16_MAX, U) * 2U + 1U)"/>
  <define name="INT32_MAX" value="0x7fffffffL"/>
  <define name="INT32_MIN" value="(-INT32_MAX - 1L)"/>
  <define name="UINT32_MAX" value="(__CONCAT(INT32_MAX, U) * 2UL + 1UL)"/>
  <define name="INT64_MAX" value="0x7fffffffffffffffLL"/>
  <define name="INT64_MIN" value="(-INT64_MAX - 1LL)"/>
  <define name="UINT64_MAX" value="(__CONCAT(INT64_MAX, U) * 2ULL + 1ULL)"/>
  <!-- Limits of minimum-width integer types -->
  <define name="INT_LEAST8_MAX" value="INT8_MAX"/>
  <define name="INT_LEAST8_MIN" value="INT8_MIN"/>
  <define name="UINT_LEAST8_MAX" value="UINT8_MAX"/>
  <define name="INT_LEAST16_MAX" value="INT16_MAX"/>
  <define name="INT_LEAST16_MIN" value="INT16_MIN"/>
  <define name="UINT_LEAST16_MAX" value="UINT16_MAX"/>
  <define name="INT_LEAST32_MAX" value="INT32_MAX"/>
  <define name="INT_LEAST32_MIN" value="INT32_MIN"/>
  <define name="UINT_LEAST32_MAX" value="UINT32_MAX"/>
  <define name="INT_LEAST64_MAX" value="INT64_MAX"/>
  <define name="INT_LEAST64_MIN" value="INT64_MIN"/>
  <define name="UINT_LEAST64_MAX" value="UINT64_MAX"/>
  <!-- Limits of fastest minimum-width integer types -->
  <define name="INT_FAST8_MAX" value="INT8_MAX"/>
  <define name="INT_FAST8_MIN" value="INT8_MIN"/>
  <define name="UINT_FAST8_MAX" value="UINT8_MAX"/>
  <define name="INT_FAST16_MAX" value="INT16_MAX"/>
  <define name="INT_FAST16_MIN" value="INT16_MIN"/>
  <define name="UINT_FAST16_MAX" value="UINT16_MAX"/>
  <define name="INT_FAST32_MAX" value="INT32_MAX"/>
  <define name="INT_FAST32_MIN" value="INT32_MIN"/>
  <define name="UINT_FAST32_MAX" value="UINT32_MAX"/>
  <define name="INT_FAST64_MAX" value="INT64_MAX"/>
  <define name="INT_FAST64_MIN" value="INT64_MIN"/>
  <define name="UINT_FAST64_MAX" value="UINT64_MAX"/>
  <!-- Limits of integer types capable of holding object pointers -->
  <define name="INTPTR_MAX" value="INT16_MAX"/>
  <define name="INTPTR_MIN" value="INT16_MIN"/>
  <define name="UINTPTR_MAX" value="UINT16_MAX"/>
  <!-- Limits of greatest-width integer types -->
  <define name="INTMAX_MAX" value="INT64_MAX"/>
  <define name="INTMAX_MIN" value="INT64_MIN"/>
  <define name="UINTMAX_MAX" value="UINT64_MAX"/>
  <!-- Limits of other integer types -->
  <define name="PTRDIFF_MAX" value="INT16_MAX"/>
  <define name="PTRDIFF_MIN" value="INT16_MIN"/>
  <define name="SIG_ATOMIC_MAX" value="INT8_MAX"/>
  <define name="SIG_ATOMIC_MIN" value="INT8_MIN"/>
  <define name="SIZE_MAX" value="(__CONCAT(INT16_MAX, U))"/>
  <!-- Macros for integer constants -->
  <define name="INT8_C(value)" value="((int8_t) value)"/>
  <define name="UINT8_C(value)" value="((uint8_t) __CONCAT(value, U))"/>
  <define name="INT16_C(value)" value="value"/>
  <define name="UINT16_C(value)" value="__CONCAT(value, U)"/>
  <define name="INT32_C(value)" value="__CONCAT(value, L)"/>
  <define name="UINT32_C(value)" value="__CONCAT(value, UL)"/>
  <define name="INT64_C(value)" value="__CONCAT(value, LL)"/>
  <define name="UINT64_C(value)" value="__CONCAT(value, ULL)"/>
  <define name="INTMAX_C(value)" value="__CONCAT(value, LL)"/>
  <define name="UINTMAX_C(value)" value="__CONCAT(value, ULL)"/>
  <!-- FIXME: add macros for printf and scanf format specifiers,e.g. PRId8 -->
  <!-- math.h -->
  <define name="M_E" value="2.7182818284590452354"/>
  <define name="M_LOG2E" value="1.4426950408889634074"/>
  <define name="M_LOG10E" value="0.43429448190325182765"/>
  <define name="M_LN2" value="0.69314718055994530942"/>
  <define name="M_LN10" value="2.30258509299404568402"/>
  <define name="M_PI" value="3.14159265358979323846"/>
  <define name="M_PI_2" value="1.57079632679489661923"/>
  <define name="M_PI_4" value="0.78539816339744830962"/>
  <define name="M_1_PI" value="0.31830988618379067154"/>
  <define name="M_2_PI" value="0.63661977236758134308"/>
  <define name="M_2_SQRTPI" value="1.12837916709551257390"/>
  <define name="M_SQRT2" value="1.41421356237309504880"/>
  <define name="M_SQRT1_2" value="0.70710678118654752440"/>
  <define name="powf" value="pow"/>
  <define name="roundf" value="round"/>
  <define name="signbitf" value="signbit"/>
  <define name="sinf" value="sin"/>
  <define name="sinhf" value="sinh"/>
  <define name="sqrtf" value="sqrt"/>
  <define name="squaref" value="square"/>
  <define name="tanf" value="tan"/>
  <define name="tanhf" value="tanh"/>
  <define name="truncf" value="trunc"/>
  <!-- The rest of the trigonometric functions are defined in std.cfg -->
  <!-- stdio.h -->
  <define name="fdev_set_udata(stream, u)" value="do { (stream)- &gt; udata = u; } while(0)"/>
  <define name="fdev_get_udata(stream)" value="((stream)- &gt; udata)"/>
  <define name="fdev_setup_stream" value="(stream, put, get, rwflag)"/>
  <define name="_FDEV_SETUP_READ" value="__SRD"/>
  <define name="_FDEV_SETUP_WRITE" value="__SWR"/>
  <define name="_FDEV_SETUP_RW" value="(__SRD | __SWR)"/>
  <define name="_FDEV_ERR" value="(-1)"/>
  <define name="_FDEV_EOF" value="(-2)"/>
  <define name="FDEV_SETUP_STREAM" value="(put, get, rwflag)"/>
  <define name="putc(__c, __stream)" value="fputc(__c, __stream)"/>
  <define name="putchar(__c)" value="fputc(__c, stdout)"/>
  <define name="getc(__stream)" value="fgetc(__stream)"/>
  <define name="getchar()" value="fgetc(stdin)"/>
  <define name="SEEK_SET" value="0"/>
  <define name="SEEK_CUR" value="1"/>
  <define name="SEEK_END" value="2"/>
  <function name="vfprintf_P">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <formatstr/>
    <arg nr="2">
      <formatstr/>
    </arg>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <function name="printf_P">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <formatstr/>
    <arg nr="1">
      <formatstr/>
    </arg>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <function name="sprintf_P">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <formatstr/>
    <arg nr="2">
      <formatstr/>
    </arg>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <function name="snprintf_P">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <formatstr/>
    <arg nr="3">
      <formatstr/>
    </arg>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <function name="vsprintf_P">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <formatstr/>
    <arg nr="2">
      <formatstr/>
    </arg>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <function name="vsnprintf_P">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <formatstr/>
    <arg nr="3">
      <formatstr/>
    </arg>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <function name="fprintf_P">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <formatstr/>
    <arg nr="2">
      <formatstr/>
    </arg>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <function name="fputs_P">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <function name="puts_P">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <function name="scanf_P">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <formatstr scan="true"/>
    <arg nr="2">
      <formatstr/>
    </arg>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <function name="fscanf_P">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <formatstr scan="true"/>
    <arg nr="2">
      <formatstr/>
    </arg>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <function name="sscanf_P">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <formatstr scan="true"/>
    <arg nr="2">
      <formatstr/>
    </arg>
  </function>
  <function name="fdevopen">
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- stdlib.h -->
  <define name="__ptr_t" value="void *"/>
  <define name="RAND_MAX" value="0x7FFF"/>
  <function name="ltoa">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="3">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <function name="utoa">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="3">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <function name="ultoa">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="3">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <function name="random">
    <noreturn>false</noreturn>
    <leak-ignore/>
  </function>
  <function name="random_r">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
    </arg>
  </function>
  <function name="srandom">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <function name="dtostre">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-null/>
    </arg>
  </function>
  <function name="dtostrf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
    <arg nr="3">
      <not-uninit/>
    </arg>
    <arg nr="4">
      <not-null/>
    </arg>
  </function>
  <define name="RANDOM_MAX" value="0x7FFFFFFF"/>
  <define name="DTOSTR_ALWAYS_SIGN" value="0x01"/>
  <define name="DTOSTR_PLUS_SIGN" value="0x02"/>
  <define name="DTOSTR_UPPERCASE" value="0x04"/>
  <!-- string.h -->
  <function name="ffs">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <function name="ffsl">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <function name="ffsll">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <function name="memccpy">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
    <arg nr="3">
      <not-uninit/>
    </arg>
    <arg nr="4">
      <not-uninit/>
    </arg>
  </function>
</def>
