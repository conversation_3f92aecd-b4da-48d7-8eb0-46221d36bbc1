#include <pthread.h>
#include <log.h>
#include <stdlib.h>
#include <stdio.h>
#include <dfs_posix.h>
#include <mod_oak.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <elibs_stdio.h>
#include <kapi.h>
#include <hal_sem.h>

static int oak_cdr_capture(int argc, char **argv)
{
    int ret  = 0;
    int width = 1280;
    int height = 720;
    int fps = 30;
    char mPicturePath[256] = {0};
    CameraConfig nCameraConfig;
    TakePictureConfig nTakePictureConfig;
    FilePathConfig nPicFilePath;

    __u32 mid = 0;
    __mp *mp = 0;

    if(argc != 2)
    {
        printf("paramenter error. ");
        printf("for example: oak_cdr_capture /mnt/G/picture.jpg\n");
        return -1;
    }

    strcpy(mPicturePath, argv[1]);

    mid = esMODS_MInstall("d:\\mod\\oak.mod",0);
    if(mid == 0)
    {
        __err("oak install fail!");
        return -1;
    }

    mp = esMODS_MOpen(mid, 0);
    if(mp == NULL)
    {
        __err("oak open fail!");
        goto _exit_0;
    }

    //设置初始化参数
    ret = esMODS_MIoctrl(mp, OAK_CMD_INIT, 0, NULL);
    if(ret != EPDK_OK)
    {
        printf("ioctl OAK_CMD_INIT fail! \r\n");
        goto _exit_1;
    }

    memset(&nCameraConfig, 0, sizeof(CameraConfig));
    strcpy(nCameraConfig.mDevName, "/dev/video0");
    nCameraConfig.mType             = OAK_CAMERA_TYPE_CSI;
    nCameraConfig.mChn              = OAK_FRONT_CAMERA;
    nCameraConfig.mPicFormat        = OAK_UVC_NV21;
    nCameraConfig.mCaptureWidth     = width;
    nCameraConfig.mCaptureHeight    = height;
    nCameraConfig.mCaptureFrameRate = fps;
    esMODS_MIoctrl(mp, OAK_CMD_CAMERA_CREATE, OAK_FRONT_CAMERA, &nCameraConfig);

    //设置拍照参数
    memset(&nTakePictureConfig, 0, sizeof(TakePictureConfig));
    nTakePictureConfig.mWidth  = width;
    nTakePictureConfig.mHeight = height;
    esMODS_MIoctrl(mp, OAK_CMD_SET_TAKEPICTURE_PARA, OAK_FRONT_CAMERA, &nTakePictureConfig);

    //设置图片保存路径
    memset(&nPicFilePath, 0, sizeof(FilePathConfig));
    strcpy(nPicFilePath.mLinuxFilePath, mPicturePath);    //设置文件存储路径
    strcpy(nPicFilePath.mFileName, "PIC");                //设置文件名前缀
    esMODS_MIoctrl(mp, OAK_CMD_SET_PIC_FILE_PATH, OAK_FRONT_CAMERA, &nPicFilePath); 

    ret = esMODS_MIoctrl(mp, OAK_CMD_TAKE_PICTURE, OAK_FRONT_CAMERA, 0);
    if(ret == EPDK_OK)
    {
        printf("take [%s] success.\n", mPicturePath);
    }
    else
    {
        printf("take [%s] fail!\n", mPicturePath);      
    }

    esMODS_MIoctrl(mp, OAK_CMD_DEINIT, 0, 0);

_exit_1:
    esMODS_MClose(mp);

_exit_0:
    esMODS_MUninstall(mid);

out:
    return 0;
}

FINSH_FUNCTION_EXPORT_ALIAS(oak_cdr_capture, __cmd_oak_cdr_capture, oak_cdr_capture);
