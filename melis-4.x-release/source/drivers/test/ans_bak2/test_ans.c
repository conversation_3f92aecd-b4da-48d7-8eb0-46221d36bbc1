// WebRtcAudioTest.c : Defines the entry point for the console application.
//
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <rtthread.h>

#include "noise_suppression.h"

//#include "webrtc/modules/audio_processing/agc/include/gain_control.h"
//#include "webrtc/common_audio/signal_processing/include/signal_processing_library.h"

//#include <math.h>

#ifndef _CRT_SECURE_NO_WARNINGS
#define _CRT_SECURE_NO_WARNINGS
#endif

//#include "project_ns.h"

#define AUDIO_CHANNEL 1
#include "nosc.h"
#include "gain_control.h"
#include "eq.h"

#define NS_SAMPLE_RATE  (16000)
#define FRAME_SIZE      (320)
#define CHANNUM         (1)
#define BUFLEN          (FRAME_SIZE*CHANNUM)

void nosc_process(void)
{
	FILE* fin;
	FILE* fout;
	int items1;
	int count = 0;
	void* nosc;
	short bufferin[BUFLEN];
	ns_prms_t* prms = (ns_prms_t*)malloc(sizeof(ns_prms_t));

	fin = fopen("f:\\audio_data_f133\\aec_out.pcm","rb");
	fout = fopen("f:\\audio_data_f133\\nosc_out.pcm","wb");
	
	prms->sampling_rate = NS_SAMPLE_RATE;
	prms->max_suppression = -20;
	//prms->overlap_percent = OVERLAP_FIFTY;
	prms->overlap_percent = OVERLAP_SEVENTY_FIVE;

	prms->nonstat = MEDIUM_NONSTATIONAL;//STATIONAL;//
	prms->channum = CHANNUM;

	nosc = NOSCinit(prms);

	items1 = fread(bufferin, sizeof(short), BUFLEN, fin);
	while(items1 > 0)
	{
		NOSCdec(nosc, bufferin, items1);
		fwrite(bufferin, sizeof(short), items1, fout);
		items1 = fread(bufferin,sizeof(short), BUFLEN, fin);
	}

	NOSCdestroy(nosc);
	if (NULL != prms)
	{
        free(prms);
        prms = NULL;
	}

	fclose(fin);
	fclose(fout);
}

void WebRtcAgcTest(char *filename, char *outfilename,int fs)
{
	FILE *infp      = NULL;
	FILE *outfp     = NULL;

	short *pData    = NULL;
	short *pOutData = NULL;
	void *agcHandle = NULL;	

	do 
	{
		WebRtcAgc_Create(&agcHandle);

		int minLevel = 0;
		int maxLevel = 255;
		int agcMode = kAgcModeFixedDigital;
		WebRtcAgc_Init(agcHandle, minLevel, maxLevel, agcMode, fs);

		WebRtcAgc_config_t agcConfig;
		agcConfig.compressionGaindB = 9;
		agcConfig.limiterEnable     = 1;
		agcConfig.targetLevelDbfs   = 1;
		WebRtcAgc_set_config(agcHandle, agcConfig);

		infp = fopen(filename,"rb");
		int frameSize = 160;//80;//
		pData    = (short*)malloc(frameSize*sizeof(short));
		pOutData = (short*)malloc(frameSize*sizeof(short));

		outfp = fopen(outfilename,"wb");
		int len = frameSize*sizeof(short);
		int micLevelIn = 0;
		int micLevelOut = 0;
		while(1)
		{
			memset(pData, 0, len);
			len = fread(pData, 1, len, infp);
			if (len > 0)
			{
				int inMicLevel  = micLevelOut;
				int outMicLevel = 0;
				uint8_t saturationWarning;
				int nAgcRet = WebRtcAgc_Process(agcHandle, pData, NULL, frameSize, pOutData,NULL, inMicLevel, &outMicLevel, 0, &saturationWarning);
				if (nAgcRet != 0)
				{
					printf("failed in WebRtcAgc_Process\n");
					break;
				}
				micLevelIn = outMicLevel;
				fwrite(pOutData, 1, len, outfp);
			}
			else
			{
				break;
			}
		}
	} while (0);

	fclose(infp);
	fclose(outfp);
	free(pData);
	free(pOutData);
	WebRtcAgc_Free(agcHandle);
}

eq_core_prms_t core_prms[] =
{
    //{12, 100, 0.0, LOWPASS_SHELVING},
    //{-12, 200, 2.0, BANDPASS_PEAK},
    {-15, 1000, 1.9, BANDPASS_PEAK},
    //{-12, 4000, 0.3, BANDPASS_PEAK},
    //{12, 16000, 0.0, HIHPASS_SHELVING},
};

int final_eq_process(void)
{
    short buffer[4096];
    void* equalizer;
    FILE* in = NULL;
    FILE* out = NULL;
    int bin_num = 10, samplerate =16000, chan = 1;
    int items, idx=0;
    int freq, gain;
    int temp = 0;
    float q;
    
    eq_prms_t prms;

	in = fopen("f:\\audio_data_f133\\agc_out.pcm", "rb");
	out = fopen("f:\\audio_data_f133\\eq_out.pcm","wb");

#if 0 // input the parameter

	printf("How many argvs : %d", argvs);
    if(argvs <= 1)
    {
        usage();
        return -1;
    }
    
    while(idx < argvs)
    {
        if(!strcmp(argv[idx], "-i"))
        {
            idx++;
            in = fopen(argv[idx], "rb");
            if(!in)
            {
				printf("Invaild input file!");
                return -1;
            }
            idx++;
        }
        if(!strcmp(argv[idx], "-o"))
        {
            idx++;
            out = fopen(argv[idx], "wb");
            if(!out)
            {
				printf("Invaild Output file!");
                return -1;
            }
            idx++;
        }
        if(!strcmp(argv[idx], "-n"))
        {
            idx++;
            bin_num=atoi(argv[idx]);
        }
        if(!strcmp(argv[idx], "-r"))
        {
            idx++;
            samplerate=atoi(argv[idx]);
        }
        if(!strcmp(argv[idx], "-c"))
        {
            idx++;
            chan=atoi(argv[idx]);
        }
        idx++;
    }

	printf("bin_num : %d", bin_num);
	printf("samplerate : %d", samplerate);

    memset(&prms, 0x00, sizeof(eq_prms_t));

    prms.biq_num = bin_num;
    prms.sampling_rate = samplerate;
    prms.chan = chan;	
    prms.core_prms = calloc(sizeof(eq_core_prms_t), bin_num);
    
    idx = 0;
    while(idx < bin_num)
    {
		printf("****************** %d *************", idx + 1);
		printf("\n****************** filter_types *************\n");
		printf("%d:Low_freq_shelving\n", LOWPASS_SHELVING);
		printf("%d:Bandpass_peak\n", BANDPASS_PEAK);
		printf("%d:High_freq_shelving\n", HIHPASS_SHELVING);
		printf("%d:Low_pass\n", LOWPASS);
		printf("%d:High_pass\n", HIGHPASS);
		printf("please input the filter type you want:\n");
		scanf("%d", &temp);
		switch (temp)
		{
		case LOWPASS_SHELVING:
			prms.core_prms[idx].type = LOWPASS_SHELVING;
			printf("Low_freq_shelving\n");

			printf("* freq point(hz) : ");
			scanf("%d", &freq);
			prms.core_prms[idx].fc = freq;

			printf("* gain( -20 - 20 db) : ");
			scanf("%d", &gain);
			prms.core_prms[idx].G = gain;

			prms.core_prms[idx].Q = 1;

			break;
		case BANDPASS_PEAK:
			prms.core_prms[idx].type = BANDPASS_PEAK;
			printf("Bandpass_peak\n");

			printf("* freq point(hz) : ");
			scanf("%d", &freq);
			prms.core_prms[idx].fc = freq;

			printf("* gain( -20 - 20 db) : ");
			scanf("%d", &gain);
			prms.core_prms[idx].G = gain;

			printf("* q( the bigger, the narrower: the smaller, the wider ) : ");
			scanf("%f", &q);
			prms.core_prms[idx].Q = q;
			break;
		case HIHPASS_SHELVING:
			prms.core_prms[idx].type = HIHPASS_SHELVING;
			printf("High_freq_shelving\n");

			printf("* freq point(hz) : ");
			scanf("%d", &freq);
			prms.core_prms[idx].fc = freq;

			printf("* gain( -20 - 20 db) : ");
			scanf("%d", &gain);
			prms.core_prms[idx].G = gain;

			prms.core_prms[idx].Q = 1;
			break;
		case LOWPASS:
			prms.core_prms[idx].type = LOWPASS;
			printf("Low_pass\n");

			printf("* freq point(hz) : ");
			scanf("%d", &freq);
			prms.core_prms[idx].fc = freq;
			prms.core_prms[idx].G = 0;
			prms.core_prms[idx].Q = 1;
			break;
		case HIGHPASS:
			prms.core_prms[idx].type = HIGHPASS;
			printf("High_pass\n");

			printf("* freq point(hz) : ");
			scanf("%d", &freq);
			prms.core_prms[idx].fc = freq;
			prms.core_prms[idx].G = 0;
			prms.core_prms[idx].Q = 1;
			break;
		default:
			printf("error input");
			return -1;
			break;
		}
        idx++;
    }

#endif


	chan = 1;
	prms.biq_num = 4;
	prms.sampling_rate = 16000;
	prms.chan = chan;
	prms.core_prms = calloc(sizeof(eq_core_prms_t), 4);

	prms.core_prms[0].type = BANDPASS_PEAK;
	prms.core_prms[0].fc = 600;
	prms.core_prms[0].G = -7;
	prms.core_prms[0].Q = 2;


	prms.core_prms[1].type = BANDPASS_PEAK;
	prms.core_prms[1].fc = 1116;
	prms.core_prms[1].G = 1;
	prms.core_prms[1].Q = 1;


	prms.core_prms[2].type = BANDPASS_PEAK;
	prms.core_prms[2].fc = 1750;
	prms.core_prms[2].G = 3;
	prms.core_prms[2].Q = 1;

	prms.core_prms[3].type = LOWPASS;
	prms.core_prms[3].fc = 3700;
	prms.core_prms[3].G = 1;
	prms.core_prms[3].Q = 1;



#if 0

	prms.core_prms[4].type = BANDPASS_PEAK;
	prms.core_prms[4].fc = 435;
	prms.core_prms[4].G = 0;
	prms.core_prms[4].Q = 1;

	prms.core_prms[5].type = BANDPASS_PEAK;
	prms.core_prms[5].fc = 690;
	prms.core_prms[5].G = 0;
	prms.core_prms[5].Q = 1;

	prms.core_prms[6].type = BANDPASS_PEAK;
	prms.core_prms[6].fc = 1127;
	prms.core_prms[6].G = 1;
	prms.core_prms[6].Q = 1;

	prms.core_prms[7].type = BANDPASS_PEAK;
	prms.core_prms[7].fc = 2195;
	prms.core_prms[7].G = 0;
	prms.core_prms[7].Q = 1;

	prms.core_prms[8].type = BANDPASS_PEAK;
	prms.core_prms[8].fc = 5050;
	prms.core_prms[8].G = 0;
	prms.core_prms[8].Q = 1;

	prms.core_prms[9].type = BANDPASS_PEAK;
	prms.core_prms[9].fc = 23000;
	prms.core_prms[9].G = 0;
	prms.core_prms[9].Q = 1;
#endif


    equalizer = eq_create(&prms);
    if(equalizer == NULL)
    {
		printf("create equalizer handle error!");
		fclose(in);
		fclose(out);
        return -1;
    }

	items = fread(buffer, chan*sizeof(short), 1024, in);  //return 1024

    while(items > 0)
    {
        eq_process(equalizer, buffer, items);
        items = fwrite(buffer, chan*sizeof(short), items, out);
        items = fread(buffer, chan*sizeof(short), 1024, in);
		//printf("items = %d \n", items);//1024
	}

    eq_destroy(equalizer);
	free(prms.core_prms);

    fclose(in);
    fclose(out);

    return 0;
}
void WebRtcNS32KSample(char *szFileIn,char *szFileOut,int nSample,int nMode)
{
	int nRet = 0;
	NsHandle *pNS_inst = NULL;
	FILE *fpIn = NULL;
	FILE *fpOut = NULL;
	char *pInBuffer =NULL;
	char *pOutBuffer = NULL;
	static int run_once = 0;

	nMode = 2; //aggressive

	do
	{
		int i = 0;
		int nFileSize = 0;
		int nTime = 0;
		WebRtcNs_Create(&pNS_inst);
		WebRtcNs_Init(pNS_inst,nSample);
		WebRtcNs_set_policy(pNS_inst,nMode);

		fpIn = fopen(szFileIn, "rb");
		fpOut = fopen(szFileOut, "wb");
		if (NULL == fpIn || NULL == fpOut)
		{
			printf("open file error. \n");
			break;
		}

		fseek(fpIn,0,SEEK_END);
		nFileSize = ftell(fpIn); 
		fseek(fpIn,0,SEEK_SET); 
		pInBuffer = (char*)malloc(nFileSize);
        if(NULL == pInBuffer)
        {
            printf("ans fatal error pInBuffer buffer malloc failed!\n");
            break;
        }
		memset(pInBuffer,0,nFileSize);
		fread(pInBuffer, sizeof(char), nFileSize, fpIn);

		pOutBuffer = (char*)malloc(nFileSize);
        if(NULL == pOutBuffer)
        {
            printf("ans fatal error pOutBuffer buffer malloc failed!\n");
            break;
        }
		memset(pOutBuffer,0,nFileSize);

		static int  filter_state1[6],filter_state12[6];
		static int  Synthesis_state1[6],Synthesis_state12[6];

        if (++run_once == 1)
        {
    		memset(filter_state1,0,sizeof(filter_state1));
    		memset(filter_state12,0,sizeof(filter_state12));
    		memset(Synthesis_state1,0,sizeof(Synthesis_state1));
    		memset(Synthesis_state12,0,sizeof(Synthesis_state12));
    		run_once = 1;
        }

		for (i = 0;i < nFileSize;i+=640)
		{
			if (nFileSize - i >= 640)
			{
				short shBufferIn[320] = {0};
				short shInL[160],shInH[160];
				short shOutL[160] = {0},shOutH[160] = {0};
				memcpy(shBufferIn,(char*)(pInBuffer+i),320*sizeof(short));

				WebRtcSpl_AnalysisQMF(shBufferIn,shInL,shInH,filter_state1,filter_state12);

				if (0 == WebRtcNs_Process(pNS_inst ,shInL  ,shInH ,shOutL , shOutH))
				{
					short shBufferOut[320];

					WebRtcSpl_SynthesisQMF(shOutL,shOutH,shBufferOut,Synthesis_state1,Synthesis_state12);
					memcpy(pOutBuffer+i,shBufferOut,320*sizeof(short));
				}
			}
		}

		fwrite(pOutBuffer, sizeof(char), nFileSize, fpOut);
	} while (0);

	WebRtcNs_Free(pNS_inst);

    if (NULL != pOutBuffer)
    {
        free(pOutBuffer);
        pOutBuffer = NULL;
    }
    if (!pInBuffer)
    {
    	free(pInBuffer);
    	pInBuffer = NULL;
    }
	fclose(fpIn);
	fclose(fpOut);
}

int main_ans_test(int argc, const char **argv)
{
    static int count_star;
    if (count_star == 0)
    {
        WebRtcNS32KSample("f:\\audio_data_f133\\micin.pcm", "f:\\audio_data_f133\\ans_out.pcm", 32000, 2);    // 16K hz 单声道音频, 参数对应为32000.
    	printf("ANS (Ambient Noise Supression) end!\n");
    }
	if (++count_star >= 2)
	{
        nosc_process();
        printf("nosc_process end!\n");
    //  project_ns_process();
    //  printf("Project_ns end!\n");
        WebRtcAgcTest("f:\\audio_data_f133\\nosc_out.pcm","f:\\audio_data_f133\\agc_out.pcm",16000);
        printf("webrtc agc end!\n");
        final_eq_process();
        printf("final_eq_process end!\n");
	}

	getchar();
	return 0;
}

FINSH_FUNCTION_EXPORT_ALIAS(main_ans_test, __cmd_ans_test, ans_test_demo);

#if 0
void project_ns_process(void)
{
	short as_audio_in[NS_PERIOD_SIZE * AUDIO_CHANNEL];
	short as_audio_agc[NS_PERIOD_SIZE * AUDIO_CHANNEL];
	short as_audio_out[NS_PERIOD_SIZE * AUDIO_CHANNEL];
	SpeexPreprocessState *st_ns_handle[2];
	struct USER_FILTER handle = { 0 };
	agc_handle * agc_demo = NULL;
	FILE *pfread = NULL;
	FILE *pfwrite = NULL;
	
	pfread = fopen("f:\\audio_data_f133\\nosc_out.pcm", "rb");
	pfwrite = fopen("f:\\audio_data_f133\\agc_out.pcm", "wb");

	if ((NULL == pfread) || (NULL == pfwrite))
	{
		printf("project_ns_process fopen_error!!!\n");
		goto ERR_EXIT;
	}

	func_ns_init(NS_PERIOD_SIZE, 16000, AUDIO_CHANNEL, &st_ns_handle[0], &st_ns_handle[1]);
	agc_demo = func_agc_init(16000, 1, -3);    // 可调节参数-3表示目标输出db位置, 浮点数、范围0至负无穷, 0db对应最大agc效果, 可设置为小数, 如-0.5L.
//	func_bpf_init(&handle, 16000, 7000, 100);   // 可调节参数7000 100表示仅保留100Hz-7000Hz的音频, 其他频率将被滤除.

	while (fread(as_audio_in, sizeof(short), NS_PERIOD_SIZE * AUDIO_CHANNEL, pfread) == NS_PERIOD_SIZE * AUDIO_CHANNEL)
	{
		func_ns_proc(st_ns_handle, as_audio_in, AUDIO_CHANNEL);
		func_agc_proc(agc_demo, as_audio_in, as_audio_agc, NS_PERIOD_SIZE * AUDIO_CHANNEL);
//		func_bpf_proc(&handle, as_audio_agc, as_audio_out);  // N_DATA_LEN;

		fwrite(as_audio_agc, sizeof(short), NS_PERIOD_SIZE * AUDIO_CHANNEL, pfwrite);
	}

	func_agc_exit(agc_demo);
	func_ns_exit(st_ns_handle);
ERR_EXIT:
    if (NULL != pfread)
    {
    	fclose(pfread);
    }
    if (NULL != pfwrite)
    {
    	fclose(pfwrite);
    }
}

#endif
