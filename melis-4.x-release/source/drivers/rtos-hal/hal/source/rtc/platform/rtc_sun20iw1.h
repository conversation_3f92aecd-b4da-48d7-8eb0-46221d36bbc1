/* Copyright (c) 2019-2025 Allwinner Technology Co., Ltd. ALL rights reserved.

 * Allwinner is a trademark of Allwinner Technology Co.,Ltd., registered in
 * the the People's Republic of China and other countries.
 * All Allwinner Technology Co.,Ltd. trademarks are used with permission.

 * DISCLAIMER
 * THIRD PARTY LICENCES MAY BE REQUIRED TO IMPLEMENT THE SOLUTION/PRODUCT.
 * IF YOU NEED TO INTEGRATE THIRD PARTY��S TECHNOLOGY (SONY, DTS, DOLBY, AVS OR MPEGLA, ETC.)
 * IN ALLWINNERS��SDK OR PRODUCTS, YOU SHALL BE SOLELY RESPONSIBLE TO OBTAIN
 * ALL APPROPRIATELY REQUIRED THIRD PARTY LICENCES.
 * ALLWINNER SHALL HAVE NO WARRANTY, INDEMNITY OR OTHER OBLIGATIONS WITH RESPECT TO MATTERS
 * COVERED UNDER ANY REQUIRED THIRD PARTY LICENSE.
 * YOU ARE SOLELY RESPONSIBLE FOR YOUR USAGE OF THIRD PARTY��S TECHNOLOGY.


 * THIS SOFTWARE IS PROVIDED BY ALLWINNER"AS IS" AND TO THE MAXIMUM EXTENT
 * PERMITTED BY LAW, ALLWINNER EXPRESSLY DISCLAIMS ALL WARRANTIES OF ANY KIND,
 * WHETHER EXPRESS, IMPLIED OR STATUTORY, INCLUDING WITHOUT LIMITATION REGARDING
 * THE TITLE, NON-INFRINGEMENT, ACCURACY, CONDITION, COMPLETENESS, PERFORMANCE
 * OR MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
 * IN NO EVENT SHALL ALLWINNER BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
 * NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS, OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED
 * OF THE POSSIBILITY OF SUCH DAMAGE.

 */

#ifndef __RTC_SUN20IW1_H__
#define __RTC_SUN20IW1_H__

#undef BIT
#define BIT(nr)     (1UL << (nr))

#define SUNXI_RTC_BASE      0x07090000
#define SUNXI_RTC_DATA_BASE     (SUNXI_RTC_BASE+0x100)

#define SUNXI_GIC_START 32
#define SUXNI_IRQ_RTC (SUNXI_GIC_START + 128)

#define SUNXI_SIMPLIFIED_TIMER      1

#define SUNXI_RTC_YMD               0x0010

#define SUNXI_RTC_HMS               0x0014

#define SUNXI_ALRM_DAY              0X0020
#define SUNXI_ALRM_COUNTER                      0x0020
#define SUNXI_ALRM_CURRENT                      0x0024
#define SUNXI_ALRM_HMS              0X0024

#define SUNXI_ALRM_EN                           0x0028
#define SUNXI_ALRM_EN_CNT_EN                    BIT(0)

#define SUNXI_ALRM_IRQ_EN                       0x002c
#define SUNXI_ALRM_IRQ_EN_CNT_IRQ_EN            BIT(0)

#define SUNXI_ALRM_IRQ_STA                      0x0030
#define SUNXI_ALRM_IRQ_STA_CNT_IRQ_PEND         BIT(0)

#define SUNXI_LOSC_CTRL             0x0000
//#define SUNXI_LOSC_CTRL_RTC_ALARM_ACC       BIT(9)
#define SUNXI_LOSC_CTRL_RTC_HMS_ACC     BIT(8)
#define SUNXI_LOSC_CTRL_RTC_YMD_ACC     BIT(7)
#define REG_LOSCCTRL_MAGIC          0x16aa0000
#define REG_CLK32K_AUTO_SWT_EN          BIT(14)
#define REG_CLK32K_AUTO_SWT_BYPASS      BIT(15)
#define RTC_SOURCE_EXTERNAL         0x00000001
#define EXT_LOSC_GSM                0x00000008
#define SUNXI_ALARM_CONFIG                      0x0050
#define SUNXI_ALRM_WAKEUP_OUTPUT_EN             BIT(0)

#define RTC_CLK_R_TYPE			HAL_SUNXI_R_CCU
#define RTC_CLK_R_ID			CLK_R_AHB_BUS_RTC
#define	RTC_CLK_RTC1K_TYPE		HAL_SUNXI_RTC_CCU
#define RTC_CLK_RTC1K_ID		CLK_RTC_1K
#define RTC_CLK_RTCSPI_TYPE		HAL_SUNXI_RTC_CCU
#define RTC_CLK_RTCSPI_ID		CLK_RTC_SPI
#define RTC_ERSET_TYPE			HAL_SUNXI_R_RESET
#define RTC_RESET_ID			RST_R_AHB_BUS_RTC

#endif /* __RTC-SUN20IW1_H__ */
