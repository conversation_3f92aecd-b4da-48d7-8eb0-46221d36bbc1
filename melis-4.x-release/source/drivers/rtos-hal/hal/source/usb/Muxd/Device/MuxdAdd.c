/*
********************************************************************************************************************
*                                              usb host driver
*
*                              (c) Copyright 2007-2010, javen.China
*										All	Rights Reserved
*
* File Name 	: LunMgr.c
*
* Author 		: javen
*
* Version 		: 2.0
*
* Date 			: 2010.03.02
*
* Description 	:
*
* History 		:
*
********************************************************************************************************************
*/
//#include  "usb_host_config.h"
//#include  "usb_host_base_types.h"
#include  "usb_os_platform.h"
#include  "error.h"

#include  "usb_muxd_i.h"
#include  "muxd.h"
#include  "muxdadd.h"

extern __s32 MuxdProbe(__muxdLun_t *muxdLun);
extern __s32 MuxdRemove(__muxdLun_t *muxdLun);

/*
*******************************************************************************
*                     muxdLun_alloc
*
* Description:
*    
*
* Parameters:
*    
* 
* Return value:
*    
*
* note:
*    
*
*******************************************************************************
*/
__muxdLun_t *muxd_alloc(void)
{
    __muxdLun_t * muxdLun = NULL;

	//muxdLun = USB_OS_MALLOC(sizeof(__muxdLun_t), USB_MEM_FILE_TRIGGER, USB_MEM_LINE_TRIGGER);
	muxdLun = (__muxdLun_t *)hal_malloc(sizeof(__muxdLun_t));
	if(muxdLun == NULL){
		DMSG_PANIC("ERR: USB_OS_MALLOC failed\n");
		return NULL;
	}

	memset(muxdLun, 0, sizeof(__muxdLun_t));

	//--<2>--create lock
	muxdLun->Lock = hal_sem_create(1);
	if(muxdLun->Lock == NULL){
		DMSG_PANIC("ERR: UsbBlkDevAllocInit: create lock failed\n");
		hal_free(muxdLun);
		return NULL;
	}

	return muxdLun;
}

/*
*******************************************************************************
*                     muxdLun_free
*
* Description:
*    
*
* Parameters:
*    
* 
* Return value:
*    
*
* note:
*    
*
*******************************************************************************
*/
void muxd_free(__muxdLun_t * muxdLun)
{
    __u8 err = 0;

    if(muxdLun == NULL){
		DMSG_PANIC("ERR: input error\n");
		return;
	}

	if(muxdLun->Lock){
		hal_sem_delete(muxdLun->Lock);
		muxdLun->Lock = NULL;		
	}else{
	    DMSG_PANIC("ERR: BlkDev Lock had already delete\n");
	}

	hal_free(muxdLun);
}


/*
*******************************************************************************
*                     muxdLunAdd
*
* Description:
*    
*
* Parameters:
*    
* 
* Return value:
*    
*
* note:
*    
*
*******************************************************************************
*/
__s32 muxdAdd(__muxdLun_t *muxdLun)
{
    __muxdDev_t 		*muxdDev 		= NULL;
	__InquiryData_t *InquiryData 	= NULL;
	__s32 ret = 0;
	__u32 i   = 0;

	if(muxdLun == NULL){
		DMSG_PANIC("ERR: MscLunAdd: input error\n");
		return USB_ERR_BAD_ARGUMENTS;
	}

    muxdDev = muxdLun->muxdDev;
	if(muxdDev == NULL){
		DMSG_PANIC("ERR: MscLunAdd: muxdDev == NULL\n");
		return -1;
	}

	muxdLun->Probe  = MuxdProbe;
	muxdLun->Remove = MuxdRemove;

			
	/* LUN̽�� */
	if(muxdLun->Probe){
		ret = muxdLun->Probe(muxdLun);
		if(ret != 0){
			DMSG_PANIC("[Lun]: MscLunAdd: Lun(%d) Probe fialed\n", muxdLun->LunNo);
			return USB_ERR_DEVICE_PROBE_FAILED;
		}
	}

	return USB_ERR_SUCCESS;
}

/*
*******************************************************************************
*                     muxdLunDel
*
* Description:
*    
*
* Parameters:
*    
* 
* Return value:
*    
*
* note:
*    
*
*******************************************************************************
*/
__s32 muxdDel(__muxdLun_t *muxdLun)
{
	__s32 ret = 0;

	if(muxdLun == NULL){
		DMSG_PANIC("ERR: MscLunDel: input error\n");
		return USB_ERR_BAD_ARGUMENTS;
	}

	if(muxdLun->Remove){
		ret = muxdLun->Remove(muxdLun);
		if(ret != 0){
			DMSG_PANIC("[Lun]: MscLunAdd: Lun(%d) Remove fialed\n", muxdLun->LunNo);
			return USB_ERR_DEVICE_REMOVE_FAILED;
		}
	}
	return USB_ERR_SUCCESS;
}




