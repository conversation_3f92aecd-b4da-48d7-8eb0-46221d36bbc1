/*
********************************************************************************************************************
*                                              usb host driver
*
*                              (c) Copyright 2007-2010, javen.China
*										All	Rights Reserved
*
* File Name 	: UsbMuxdDev.c
*
* Author 		: javen
*
* Version 		: 2.0
*
* Date 			: 2010.03.02
*
* Description 	:
*
* History 		:
*
********************************************************************************************************************
*/

//#include  "usb_host_config.h"
//#include  "usb_host_base_types.h"
#include  "usb_os_platform.h"
#include  "error.h"

//#include  "usbh_debug.h"
#include  "usb_gen_hub.h"

#include  "usbh_buff_manager.h"

#include  "usb_muxd_i.h"
#include  "muxd.h"

#include  "MuxdDev.h"
#include  "../Protocol/muxdTransport.h"


#if 0
#define __log(...)   do { LOG_COLOR(OPTION_LOG_LEVEL_LOG, LOG_LEVEL_DEBUG_PREFIX, SYS_LOG_COLOR_YELLOW, ##__VA_ARGS__); } while(0)
#else
#define __log(...)	do { } while(0)
#endif

#if  0
#define DMSG_INFO           __log
#else
#define DMSG_INFO(...)
#endif


//#define DMSG_INFO   eLIBs_printf

//extern __u32 usbh_get_usbd_port(void);

/*
*******************************************************************************
*                     MuxdOpen
*
* Description:
*    
*
* Parameters:
*    
* 
* Return value:
*    0	���ɹ�
*	!0	��ʧ��
*
* note:
*    ��
*
*******************************************************************************
*/
static USB_OS_HANDLE MuxdDevOpen(void *open_arg, __u32 mode)
{
    __UsbMuxdDev_t *MuxdDev = NULL;

	DMSG_PANIC("MuxdOpen\n");
	

	if(open_arg == NULL){
		DMSG_PANIC("ERR: MuxdOpen: input error, open_arg = %x\n", open_arg);
		return NULL;
	}

	MuxdDev = (__UsbMuxdDev_t *)open_arg;
	if(MuxdDev->Magic != USB_BLK_DEV_MAGIC){
		DMSG_PANIC("ERR: MuxdOpen: MuxdDev Magic(%x) is invalid\n", MuxdDev->Magic);
		return NULL;
	}

	MuxdDev->used++;

    return (USB_OS_HANDLE)open_arg;
}

/*
*******************************************************************************
*                     MuxdClose
*
* Description:
*    
*
* Parameters:
*    
* 
* Return value:
*    0	���ɹ�
*	!0	��ʧ��
*
* note:
*    ��
*
*******************************************************************************
*/
static __s32 MuxdDevClose(USB_OS_HANDLE hDev)
{
    __UsbMuxdDev_t *MuxdDev = NULL;

	DMSG_PANIC("MuxdClose\n");


	if(hDev == NULL){
		DMSG_PANIC("ERR: MuxdClose: input error, hDev = %x\n", hDev);
		return EPDK_FAIL;
	}

	MuxdDev = (__UsbMuxdDev_t *)hDev;
	if(MuxdDev->Magic != USB_BLK_DEV_MAGIC){
		DMSG_PANIC("ERR: MuxdClose: MuxdDev Magic(%x) is invalid\n", MuxdDev->Magic);
		return EPDK_FAIL;
	}

	MuxdDev->used--;

    return EPDK_OK;
}

/*
*******************************************************************************
*                     __MuxdRead
*
* Description:
*    
*
* Parameters:
*    
* 
* Return value:
*    0	���ɹ�
*	!0	��ʧ��
*
* note:
*    ��
*
*******************************************************************************
*/
static __u32 __MuxdDevRead(void * pBuffer, __u32 blk, __u32 n, USB_OS_HANDLE hDev,__u8 flag_mirror)
{
    __UsbMuxdDev_t *MuxdDev = NULL;
	__muxdLun_t *muxdLun = NULL;
	__u32 cmd_version = 10;   /* Ĭ��Ϊ10 */
	__s32 int_ret = 0;
	__s32 uint_ret = 0;	
	__s32 int_len;
	__s32 uint_len;

	if(hDev == NULL){
		DMSG_PANIC("ERR: __MuxdRead: input error, hDev = %x\n", hDev);
		return 0;
	}

	if(n == 0){
		DMSG_PANIC("ERR: __MuxdRead: the read length can not be zero\n");
		return 0;
	}

	MuxdDev = (__UsbMuxdDev_t *)hDev;
	if(MuxdDev->Magic != USB_BLK_DEV_MAGIC){
		DMSG_PANIC("ERR: __MuxdRead: MuxdDev Magic(%x) is invalid\n", MuxdDev->Magic);
		return 0;
	}

	/* ���û��ע��disk�豸, �Ͳ��ܹ������� */
	if(!MuxdDev->is_RegMuxd){
		DMSG_PANIC("ERR: __MuxdRead: Not reged Muxd, can not read\n");
		return 0;
	}

	muxdLun = MuxdDev->Lun;
	if(muxdLun == NULL){
		DMSG_PANIC("ERR: __MuxdRead: muxdLun == NULL\n");
		return 0;
	}

	set_usbh_disk_status(USB_STORAGE_DEVICE_STATUS_READ);

	
	int_len = n - (n % 512);
    if(int_len)
    {
    
	#ifdef IOS_MIRROR_MFI
	if(flag_mirror == 2)
		int_ret = MuxdCtrlRead(muxdLun, 10, blk, n, (__u8 *)pBuffer, (int_len ));
	else if(flag_mirror == 1)
		int_ret = MuxdMirrorRead(muxdLun, 10, blk, n, (__u8 *)pBuffer, (int_len ));
	else
    		int_ret = MuxdRead(muxdLun, 10, blk, n, (__u8 *)pBuffer, (int_len ));
	#else
		#ifdef IOS_MIRROR_APP_USB_READ_TIMEOUT
    	if(flag_mirror == 3)
    		int_ret = MuxdReadEx(muxdLun, 10, blk, n, (__u8 *)pBuffer, (int_len ));
		else if(flag_mirror == 1)	
		int_ret = MuxdMirrorRead(muxdLun, 10, blk, n, (__u8 *)pBuffer, (int_len ));
		else
    		int_ret = MuxdRead(muxdLun, 10, blk, n, (__u8 *)pBuffer, (int_len ));
		#else
		if(flag_mirror)	
		int_ret = MuxdMirrorRead(muxdLun, 10, blk, n, (__u8 *)pBuffer, (int_len ));
		else
    		int_ret = MuxdRead(muxdLun, 10, blk, n, (__u8 *)pBuffer, (int_len ));
		#endif
	
	#endif
    	if(int_ret <= 0){
    		DMSG_PANIC("ERR: MuxdRead%d failed. DevNo = %d, blk = %d, n = %d\n", 
    			       cmd_version, MuxdDev->DevNo, blk, n);
    		set_usbh_disk_status(USB_STORAGE_DEVICE_STATUS_IDLE);
    		return int_ret;//0;
    	}
    	else
    	{
    		DMSG_INFO("OK: MuxdRead%d successed. DevNo = %d, blk = %d, n = %d\n", 
    			       cmd_version, MuxdDev->DevNo, blk, n);		
    	}
    }
	
	uint_len = (n % 512);
	if(uint_len == 0){
		set_usbh_disk_status(USB_STORAGE_DEVICE_STATUS_IDLE);
		DMSG_PANIC("int_ret:%d,uint_ret:%d\n",int_ret,uint_ret);				
		return int_ret;
	}	
	if(flag_mirror)
		uint_ret = MuxdMirrorRead(muxdLun, 10, blk, n, (__u8 *)pBuffer+int_len, (uint_len ));
	else
	uint_ret = MuxdRead(muxdLun, 10, blk, n, (__u8 *)pBuffer+int_len, (uint_len ));
	if(uint_ret <= 0){
		DMSG_PANIC("ERR: MuxdRead%d failed. DevNo = %d, blk = %d, n = %d\n", 
			       cmd_version, MuxdDev->DevNo, blk, n);
		set_usbh_disk_status(USB_STORAGE_DEVICE_STATUS_IDLE);
		return uint_ret;//0;
	}
	else
	{
		DMSG_INFO("OK: MuxdRead%d successed. DevNo = %d, blk = %d, n = %d\n", 
			       cmd_version, MuxdDev->DevNo, blk, n);		
	}
	
	set_usbh_disk_status(USB_STORAGE_DEVICE_STATUS_IDLE);

	#if 0
	{
		__s32 i;
		for(i=0;i<n;i++)
			DMSG_ADB_TEST("%c",*((__u8 *)pBuffer+i));
		DMSG_ADB_TEST("\n");
		
	}
	#endif

	DMSG_PANIC("int_ret:%d,uint_ret:%d\n",int_ret,uint_ret);	
    return int_ret+uint_ret;
}

/*
*******************************************************************************
*                     __MuxdWrite
*
* Description:
*    
*
* Parameters:
*    
* 
* Return value:
*    0	���ɹ�
*	!0	��ʧ��
*
* note:
*    ��
*
*******************************************************************************
*/
static __u32 __MuxdDevWrite(const void * pBuffer, __u32 blk, __u32 n, USB_OS_HANDLE hDev,__u8 flag_mirror)
{
    __UsbMuxdDev_t *MuxdDev = NULL;
	__muxdLun_t *muxdLun = NULL;
	__u32 cmd_version = 10;   /* Ĭ��Ϊ10 */
	__s32 int_ret = 0;
	__s32 uint_ret = 0;	
	__s32 int_len;
	__s32 uint_len;

	
	if(hDev == NULL){
		DMSG_PANIC("ERR: __MuxdWrite: input error, hDev = %x\n", hDev);
		return 0;
	}

	if(n == 0){
		DMSG_PANIC("ERR: __MuxdWrite: the write length can not be zero\n");
		return 0;
	}

	#if 0
	{
		__s32 i;
		for(i=0;i<n;i++)
			DMSG_ADB_TEST("*pBuffer=%x\n",*((__u8 *)pBuffer+i));
	}
	#endif
	
	
	MuxdDev = (__UsbMuxdDev_t *)hDev;
	if(MuxdDev->Magic != USB_BLK_DEV_MAGIC){
		DMSG_PANIC("ERR: __MuxdWrite: MuxdDev Magic(%x) is invalid\n", MuxdDev->Magic);
		return 0;
	}

	/* ���û��ע��disk�豸, �Ͳ��ܹ������� */
	if(!MuxdDev->is_RegMuxd){
		DMSG_PANIC("ERR: __MuxdWrite: Not reged Muxd, can not read\n");
		return 0;
	}

	muxdLun = MuxdDev->Lun;
	if(muxdLun == NULL){
		DMSG_PANIC("ERR: __MuxdWrite: muxdLun == NULL\n");
		return 0;
	}

	set_usbh_disk_status(USB_STORAGE_DEVICE_STATUS_WRITE);

	
	int_len = n - (n % 512);
    if(int_len)
    {
        //UsbLock(muxdLun->Lock);
	#ifdef IOS_MIRROR_MFI
	  if(flag_mirror == 2)
		int_ret = MuxdCtrlWrite(muxdLun, 10, blk, n, (__u8 *)pBuffer, (int_len ));
	  else if(flag_mirror == 1)
		  int_ret = MuxdMirrorWrite(muxdLun, 10, blk, n, (__u8 *)pBuffer, (int_len ));
	else
    		int_ret = MuxdWrite(muxdLun, 10, blk, n, (__u8 *)pBuffer, (int_len ));
	#else
       if(flag_mirror)
		int_ret = MuxdMirrorWrite(muxdLun, 10, blk, n, (__u8 *)pBuffer, (int_len ));
	else
    	int_ret = MuxdWrite(muxdLun, 10, blk, n, (__u8 *)pBuffer, (int_len ));
	#endif
    	//UsbUnLock(muxdLun->Lock);
    	if(int_ret <= 0){
    		DMSG_PANIC("ERR: MuxdWrite%d failed. DevNo = %d, blk = %d, n = %d\n", 
    			       cmd_version, MuxdDev->DevNo, blk, n);
    		set_usbh_disk_status(USB_STORAGE_DEVICE_STATUS_IDLE);
    		return 0;
    	}
    	else
    	{
    		DMSG_INFO("OK: MuxdWrite%d successed. DevNo = %d, blk = %d, n = %d\n", 
    			       cmd_version, MuxdDev->DevNo, blk, n);		
    	}	  
	}
	
	uint_len = (n % 512);
	if(uint_len == 0){
		set_usbh_disk_status(USB_STORAGE_DEVICE_STATUS_IDLE);
		DMSG_PANIC("int_ret:%d,uint_ret:%d\n",int_ret,uint_ret);		
		return int_ret;
	}
	//UsbLock(muxdLun->Lock);
	 if(flag_mirror)
		uint_ret = MuxdMirrorWrite(muxdLun, 10, blk, n, (__u8 *)pBuffer+int_len, (uint_len ));
	 else
	uint_ret = MuxdWrite(muxdLun, 10, blk, n, (__u8 *)pBuffer+int_len, (uint_len ));	
	//UsbUnLock(muxdLun->Lock);
	if(uint_ret <= 0){
		DMSG_PANIC("ERR: MuxdWrite%d failed. DevNo = %d, blk = %d, n = %d\n", 
			       cmd_version, MuxdDev->DevNo, blk, n);
		set_usbh_disk_status(USB_STORAGE_DEVICE_STATUS_IDLE);
		return 0;
	}
	else
	{
		DMSG_INFO("OK: MuxdWrite%d successed. DevNo = %d, blk = %d, n = %d\n", 
			       cmd_version, MuxdDev->DevNo, blk, n);		
	}
		
	set_usbh_disk_status(USB_STORAGE_DEVICE_STATUS_IDLE);

	DMSG_PANIC("int_ret:%d,uint_ret:%d\n",int_ret,uint_ret);
    return int_ret+uint_ret;
}

/*
*******************************************************************************
*                     MuxdRead
*
* Description:
*    �豸��
*
* Parameters:
*    pBuffer	:  output. װ�ض�����������
*    blk		:  input.  ��ʼ����
*    n			:  input.  ��������
*    hDev   	:  input.  �豸
* 
* Return value:
*    0	���ɹ�
*	!0	��ʧ��
*
* note:
*    ��
*
*******************************************************************************
*/
static __u32 MuxdDevRead(void * pBuffer, __u32 blk, __u32 n, USB_OS_HANDLE hDev,__u8 flag_mirror)
{
    __UsbMuxdDev_t *MuxdDev = NULL;
	__muxdLun_t *muxdLun = NULL;
	__u32 cnt = 0;

	if(hDev == NULL){
		DMSG_PANIC("ERR: MuxdRead: input error, hDev = %x\n", hDev);
		return 0;
	}
	DMSG_PANIC("MuxdDevRead blk:%d,n:%d\n",blk,n);

	MuxdDev = (__UsbMuxdDev_t *)hDev;
	if(MuxdDev->Magic != USB_BLK_DEV_MAGIC){
		DMSG_PANIC("ERR: MuxdRead: MuxdDev Magic(%x) is invalid\n", MuxdDev->Magic);
		return 0;
	}

	muxdLun = MuxdDev->Lun;
	if(muxdLun == NULL){
		DMSG_PANIC("ERR: MuxdRead: muxdLun == NULL\n");
		return 0;
	}


	cnt = __MuxdDevRead(pBuffer, blk, n, hDev,flag_mirror);

	if(cnt != n){
		DMSG_PANIC("MuxdRead:cnt:%d\n",cnt);		
		return cnt;
	}

    return n;
}

/*
*******************************************************************************
*                     MuxdWrite
*
* Description:
*    �豸д
*
* Parameters:
*    pBuffer	:  input. Ҫд������
*    blk		:  input. ��ʼ����
*    n			:  input. ��������
*    hDev   	:  input. �豸
* 
* Return value:
*    0	���ɹ�
*	!0	��ʧ��
*
* note:
*    ��
*
*******************************************************************************
*/
static __u32 MuxdDevWrite(const void * pBuffer, __u32 blk, __u32 n, USB_OS_HANDLE hDev,__u8 flag_mirror)
{
    __UsbMuxdDev_t *MuxdDev = NULL;
	__muxdLun_t *muxdLun = NULL;
	__u32 cnt = 0;

	if(hDev == NULL){
		DMSG_PANIC("ERR: MuxdWrite: input error, hDev = %x\n", hDev);
		return 0;
	}

	DMSG_PANIC("MuxdDevWrite blk:%d,n:%d\n",blk,n);

	MuxdDev = (__UsbMuxdDev_t *)hDev;
	if(MuxdDev->Magic != USB_BLK_DEV_MAGIC){
		DMSG_PANIC("ERR: MuxdWrite: MuxdDev Magic(%x) is invalid\n", MuxdDev->Magic);
		return 0;
	}

	muxdLun = MuxdDev->Lun;
	if(muxdLun == NULL){
		DMSG_PANIC("ERR: __MuxdWrite: muxdLun == NULL\n");
		return 0;
	}

	cnt = __MuxdDevWrite(pBuffer, blk, n, hDev,flag_mirror);

	if(cnt != n){
		DMSG_PANIC("ERR: MuxdWrite failed, want(%d) != real(%d)\n", n, cnt);
		return 0;
	}

    return n;
}

static __u32 MuxdDevClearMirrorEP(USB_OS_HANDLE hDev)
{
    __UsbMuxdDev_t *MuxdDev = NULL;
	__muxdLun_t *muxdLun = NULL;
	__u32 cnt = 0;

	if(hDev == NULL){
		DMSG_PANIC("ERR: MuxdWrite: input error, hDev = %x\n", hDev);
		return 0;
	}

	MuxdDev = (__UsbMuxdDev_t *)hDev;
	if(MuxdDev->Magic != USB_BLK_DEV_MAGIC){
		DMSG_PANIC("ERR: MuxdWrite: MuxdDev Magic(%x) is invalid\n", MuxdDev->Magic);
		return 0;
	}

	muxdLun = MuxdDev->Lun;
	if(muxdLun == NULL){
		DMSG_PANIC("ERR: __MuxdWrite: muxdLun == NULL\n");
		return 0;
	}

	set_usbh_disk_status(USB_STORAGE_DEVICE_STATUS_WRITE);
	
	MuxdMirrorClearEP(muxdLun);

	set_usbh_disk_status(USB_STORAGE_DEVICE_STATUS_IDLE);
	
    return 0;
}

/*
*******************************************************************************
*                     MuxdIoctl
*
* Description:
*    
*
* Parameters:
*    
* 
* Return value:
*    0	���ɹ�
*	!0	��ʧ��
*
* note:
*    ��
*
*******************************************************************************
*/
static __s32 MuxdDevIoctl(USB_OS_HANDLE hDev, __u32 Cmd, __s32 Aux, void *pBuffer)
{
    __UsbMuxdDev_t *MuxdDev = NULL;
	__muxdLun_t *muxdLun = NULL;
	__u32 ret = 0;

	if(hDev == NULL){
		DMSG_PANIC("ERR: MuxdIoctl: input error, hDev = %x\n", hDev);
		return NULL;
	}
		
	MuxdDev = (__UsbMuxdDev_t *)hDev;

	muxdLun = MuxdDev->Lun;
	if(muxdLun == NULL){
		DMSG_PANIC("ERR: MuxdIoctl: muxdLun == NULL\n");
		return EPDK_FAIL;
	}

	switch (Cmd){

    		case DEV_IOC_USR_LOGIC_READ:
			//MuxdRead();(void * pBuffer, __u32 blk, __u32 n, USB_OS_HANDLE hDev)
			return MuxdDevRead(pBuffer,1,Aux,hDev,0);
			//eLIBs_printf(" MuxdIoctl DEV_IOC_USR_PHY_READ\n");

		case DEV_IOC_USR_LOGIC_WRITE:
			//MuxdWrite();(void * pBuffer, __u32 blk, __u32 n, USB_OS_HANDLE hDev)
			return MuxdDevWrite(pBuffer,1,Aux,hDev,0);
			//eLIBs_printf(" MuxdIoctl DEV_IOC_USR_PHY_WRITE\n");			
		case USB_DEVICE_IOS_MIRROR_READ:
			return MuxdDevRead(pBuffer,1,Aux,hDev,1);
		case USB_DEVICE_IOS_MIRROR_WRITE:
			return MuxdDevWrite(pBuffer,1,Aux,hDev,1);
		#ifdef IOS_MIRROR_MFI
		case USB_DEVICE_IOS_CTRL_READ:
			return MuxdDevRead(pBuffer,1,Aux,hDev,2);
		case USB_DEVICE_IOS_CTRL_WRITE:
			return MuxdDevWrite(pBuffer,1,Aux,hDev,2);	
		#endif			

		#ifdef IOS_MIRROR_APP_USB_READ_TIMEOUT
		case USB_DEVICE_IOS_READ_EX:
			return MuxdDevRead(pBuffer,1,Aux,hDev,3);

		case USB_DEVICE_IOS_WRITE_EX:
			return MuxdDevWrite(pBuffer,1,Aux,hDev,3);
		#endif			

		case USB_DEVICE_IOS_MIRROR_EP_CLRAR:
			MuxdDevClearMirrorEP(hDev);
			/*
			{
				__UsbMuxdDev_t *MuxdDev = NULL;
				__muxdLun_t *muxdLun = NULL;

				eLIBs_printf("clear ep10\n");
				if(hDev == NULL){
					DMSG_PANIC("ERR: MuxdRead: input error, hDev = %x\n", hDev);
					return -1;
				}
				MuxdDev = (__UsbMuxdDev_t *)hDev;
				if(MuxdDev->Magic != USB_BLK_DEV_MAGIC){
					DMSG_PANIC("ERR: MuxdRead: MuxdDev Magic(%x) is invalid\n", MuxdDev->Magic);
					return -1;
				}
				muxdLun = MuxdDev->Lun;
				if(muxdLun == NULL){
					DMSG_PANIC("ERR: MuxdRead: muxdLun == NULL\n");
					return -1;
				}
				eLIBs_printf("clear ep11\n");
				muxdClearHalt(muxdLun->muxdDev,muxdLun->muxdDev->MirrorIn);
				muxdClearHalt(muxdLun->muxdDev,muxdLun->muxdDev->MirrorOut);
			}*/
			break;

		case USB_DEVICE_INFO_USER_CMD_GET_SERIAL:
			{
				__UsbMuxdDev_t *MuxdDev = NULL;
				__muxdLun_t *muxdLun = NULL;
				if(hDev == NULL){
					DMSG_PANIC("ERR: MuxdRead: input error, hDev = %x\n", hDev);
					return -1;
				}
				MuxdDev = (__UsbMuxdDev_t *)hDev;
				if(MuxdDev->Magic != USB_BLK_DEV_MAGIC){
					DMSG_PANIC("ERR: MuxdRead: MuxdDev Magic(%x) is invalid\n", MuxdDev->Magic);
					return -1;
				}
				muxdLun = MuxdDev->Lun;
				if(muxdLun == NULL){
					DMSG_PANIC("ERR: MuxdRead: muxdLun == NULL\n");
					return -1;
				}
				//eLIBs_printf("get serial:%s\n",MuxdDev->device_info.DeivceInfo.Serial);
				return &MuxdDev->device_info.DeivceInfo.Serial;
			}
			break;

		case USB_DEVICE_INFO_USER_CMD_GET_LOCATION:
			{
				__UsbMuxdDev_t *MuxdDev = NULL;
				__muxdLun_t *muxdLun = NULL;
				int usb_address;
				if(hDev == NULL){
					DMSG_PANIC("ERR: MuxdRead: input error, hDev = %x\n", hDev);
					return -1;
				}
				MuxdDev = (__UsbMuxdDev_t *)hDev;
				if(MuxdDev->Magic != USB_BLK_DEV_MAGIC){
					DMSG_PANIC("ERR: MuxdRead: MuxdDev Magic(%x) is invalid\n", MuxdDev->Magic);
					return -1;
				}
				muxdLun = MuxdDev->Lun;
				if(muxdLun == NULL){
					DMSG_PANIC("ERR: MuxdRead: muxdLun == NULL\n");
					return -1;
				} 
				usb_address = muxdLun->muxdDev->pusb_dev->devnum;

				return usb_address;
			}
			break;

		case USB_DEVICE_INFO_USER_CMD_GET_PID:
			{
				__UsbMuxdDev_t *MuxdDev = NULL;
				__muxdLun_t *muxdLun = NULL;
				int uPID;
				if(hDev == NULL){
					DMSG_PANIC("ERR: MuxdRead: input error, hDev = %x\n", hDev);
					return -1;
				}
				MuxdDev = (__UsbMuxdDev_t *)hDev;
				if(MuxdDev->Magic != USB_BLK_DEV_MAGIC){
					DMSG_PANIC("ERR: MuxdRead: MuxdDev Magic(%x) is invalid\n", MuxdDev->Magic);
					return -1;
				}
				muxdLun = MuxdDev->Lun;
				if(muxdLun == NULL){
					DMSG_PANIC("ERR: MuxdRead: muxdLun == NULL\n");
					return -1;
				} 
				uPID = muxdLun->muxdDev->pusb_dev->descriptor.iProduct;

				return uPID;
			}
			break;
			
		case USB_DEVICE_IOS_STOPWORK:
			{
				__UsbMuxdDev_t *MuxdDev = NULL;
				__muxdLun_t *muxdLun = NULL;

				//eLIBs_printf("clear ep10\n");
				if(hDev == NULL){
					DMSG_PANIC("ERR: MuxdRead: input error, hDev = %x\n", hDev);
					return -1;
				}
				MuxdDev = (__UsbMuxdDev_t *)hDev;
				if(MuxdDev->Magic != USB_BLK_DEV_MAGIC){
					DMSG_PANIC("ERR: MuxdRead: MuxdDev Magic(%x) is invalid\n", MuxdDev->Magic);
					return -1;
				}
				muxdLun = MuxdDev->Lun;
				if(muxdLun == NULL){
					DMSG_PANIC("ERR: MuxdRead: muxdLun == NULL\n");
					return -1;
				}
				//eLIBs_printf("stop work\n");
				muxdLun->muxdDev->StopTransport(muxdLun->muxdDev);
			}
			break;
			
#ifdef IOS_MIRROR_MFI
		case USBH_IOS_USER_CTRL_CMD_SEND_DATA:
			{
				muxd_ctrl_send(muxdLun->muxdDev,Aux,(__u8 *)pBuffer);
			}
			break;

#endif
			
			
        default:
			DMSG_PANIC("WARN : MuxdIoctl ,cmd = %x ,not support now\n", Cmd);
			return EPDK_FAIL;
    }

    return EPDK_OK;
}

/*
*******************************************************************************
*                     UsbMuxdDevAllocInit
*
* Description:
*    
*
* Parameters:
*    
* 
* Return value:
*    0	���ɹ�
*	!0	��ʧ��
*
* note:
*    ��
*
*******************************************************************************
*/
__UsbMuxdDev_t * UsbMuxdDevAllocInit(__muxdLun_t * muxdLun)
{
	__UsbMuxdDev_t *MuxdDev = NULL;
	__u8 temp_buff[32];
	__u32 temp = 0;

	if(muxdLun == NULL){
		DMSG_PANIC("ERR: UsbMuxdDevAllocInit: input error\n");
		return NULL;
	}

	//--<1>--create a block device
	//MuxdDev = (__UsbMuxdDev_t *)USB_OS_MALLOC(sizeof(__UsbMuxdDev_t), USB_MEM_FILE_TRIGGER, USB_MEM_LINE_TRIGGER);
	
	MuxdDev = (__UsbMuxdDev_t *)hal_malloc(sizeof(__UsbMuxdDev_t));
	if(MuxdDev == NULL){
		DMSG_PANIC("ERR: UsbMuxdDevAllocInit: USB_OS_MALLOC failed\n");
		return NULL;
	}

	//USB_OS_MEMSET(MuxdDev, 0, sizeof(__UsbMuxdDev_t));
	memset(MuxdDev, 0, sizeof(__UsbMuxdDev_t));
	MuxdDev->Lun = muxdLun;

	//--<2>--create lock

	//--<3>--create DevNo
	MuxdDev->DevNo = (muxdLun->LunNo * 1) + (muxdLun->muxdDev->muxdDevNo * 10);

	//--<4>--create sub device name
	if(muxdLun->DeviceType == MUXD_DEVICE_CDROM){
		//USB_OS_MEMCPY((void *)MuxdDev->DevName, USB_CDROM_DEV_NAME, USB_OS_STRLEN(USB_CDROM_DEV_NAME));
		//USB_OS_STRCAT((char *)MuxdDev->DevName, "_");
		memcpy((void *)MuxdDev->DevName, USB_CDROM_DEV_NAME, strlen(USB_CDROM_DEV_NAME));
		strcat((char *)MuxdDev->DevName, "_");
	}else{
		//USB_OS_MEMCPY((void *)MuxdDev->DevName, USB_STORAGE_DEV_NAME, USB_OS_STRLEN(USB_STORAGE_DEV_NAME));
		//USB_OS_STRCAT((char *)MuxdDev->DevName, "_");
		memcpy((void *)MuxdDev->DevName, USB_STORAGE_DEV_NAME, strlen(USB_STORAGE_DEV_NAME));
		strcat((char *)MuxdDev->DevName, "_");
	}

	/* usb controler number */
	temp = 0;//usbh_get_usbd_port();
	memset(temp_buff, 0, 32);
	Usb_uint2str_dec(temp, (char *)temp_buff);
	strcat((char *)MuxdDev->DevName, (const char *)temp_buff);

    /* muxdDevNo */
	memset(temp_buff, 0, 32);
	Usb_uint2str_dec(muxdLun->muxdDev->muxdDevNo, (char *)temp_buff);
	strcat((char *)MuxdDev->DevName, (const char *)temp_buff);

	/* LunNo */
	memset(temp_buff, 0, 32);
	Usb_uint2str_dec(muxdLun->LunNo, (char *)temp_buff);
	strcat((char *)MuxdDev->DevName, (const char *)temp_buff);

    /* init device operation function */
	MuxdDev->MuxdOp.Open  = MuxdDevOpen;
	MuxdDev->MuxdOp.Close = MuxdDevClose;
	MuxdDev->MuxdOp.Read  = MuxdDevRead;
	MuxdDev->MuxdOp.Write = MuxdDevWrite;
	MuxdDev->MuxdOp.Ioctl = MuxdDevIoctl;

	return MuxdDev;
}

/*
*******************************************************************************
*                     UsbMuxdDevFree
*
* Description:
*    
*
* Parameters:
*    
* 
* Return value:
*    0	���ɹ�
*	!0	��ʧ��
*
* note:
*    ��
*
*******************************************************************************
*/
__s32 UsbMuxdDevFree(__UsbMuxdDev_t *MuxdDev)
{
	if(MuxdDev == NULL){
		DMSG_PANIC("ERR: UsbMuxdDevFree: input error\n");
		return -1;
	}

	MuxdDev->MuxdOp.Open  = NULL;
	MuxdDev->MuxdOp.Close = NULL;
	MuxdDev->MuxdOp.Read  = NULL;
	MuxdDev->MuxdOp.Write = NULL;
	MuxdDev->MuxdOp.Ioctl = NULL;

	hal_free(MuxdDev);

	return 0;
}

/*
*******************************************************************************
*                     PrintDevStatus
*
* Description:
* 
*
* Parameters:
*    
* 
* Return value:
*    
*
* note:
*    
*
*******************************************************************************
*/
static void PrintDevStatus(__u8 *FunName, __s32 status)
{
    DMSG_INFO("FunName: %s,", FunName);
	switch(status){
		case  USB_STATUS_SUCCESS:
			DMSG_INFO("USB_STATUS_SUCCESS\n");
		break;

		case  USB_STATUS_DEVICE_DISCONNECTED:
			DMSG_INFO("USB_STATUS_DEVICE_DISCONNECTED\n");
		break;
		
		case USB_STATUS_IO_TIMEOUT:
			DMSG_INFO("USB_STATUS_IO_TIMEOUT\n");
		break;
		
		case USB_STATUS_IO_DEVICE_ERROR:
			DMSG_INFO("USB_STATUS_IO_DEVICE_ERROR\n");
		break;
		
		case USB_STATUS_DEVICE_BUSY:
			DMSG_INFO("USB_STATUS_DEVICE_BUSY\n");
		break;

		case USB_STATUS_BUFFER_TOO_SMALL:
			DMSG_INFO("USB_STATUS_BUFFER_TOO_SMALL\n");
		break;
		
		case USB_STATUS_INVALID_COMMAND:
			DMSG_INFO("USB_STATUS_INVALID_COMMAND\n");
		break;
		
		case USB_STATUS_INVALID_FIELD_IN_COMMAND:
			DMSG_INFO("USB_STATUS_INVALID_FIELD_IN_COMMAND\n");
		break;
		
		case USB_STATUS_LOGICAL_BLOCK_ADDRESS_OUT_OF_RANGE:
			DMSG_INFO("USB_STATUS_LOGICAL_BLOCK_ADDRESS_OUT_OF_RANGE\n");
		break;

		case USB_STATUS_MEDIA_NOT_PRESENT:
			DMSG_INFO("USB_STATUS_MEDIA_NOT_PRESENT\n");
		break;
		
		case USB_STATUS_NOT_READY_TO_READY_TRANSITION:
			DMSG_INFO("USB_STATUS_NOT_READY_TO_READY_TRANSITION\n");
		break;
		
		case USB_STATUS_UNRECOGNIZED_MEDIA:
			DMSG_INFO("USB_STATUS_UNRECOGNIZED_MEDIA\n");
		break;

		case USB_STATUS_UNKOWN_ERROR:
			DMSG_INFO("USB_STATUS_UNKOWN_ERROR\n");
		break;

		default:
			DMSG_INFO("unkonw status %d\n", status);
	}

    return ;
}

static void PrintMuxdInfo(__UsbMuxdDev_t *MuxdDev)
{
	__muxdLun_t * muxdLun = NULL;

	if(MuxdDev == NULL){
		DMSG_PANIC("ERR: PrintMuxdInfo: input error, MuxdDev = %x\n", MuxdDev);
		return ;
	}

	muxdLun = MuxdDev->Lun;
	if(muxdLun == NULL){
		DMSG_PANIC("ERR: UnitReady: muxdLun == NULL\n");
		return ;
	}

	DMSG_INFO("-----------------Muxd Information-----------------\n");
	DMSG_INFO("WriteProtect = %d\n", muxdLun->WriteProtect);
	DMSG_INFO("MediaPresent = %d\n", muxdLun->MediaPresent);
	DMSG_INFO("WCE          = %d\n", muxdLun->WCE);
	DMSG_INFO("RCD          = %d\n", muxdLun->RCD);
	DMSG_INFO("capacity     = %dM, sector number = %d\n", 
									 (muxdLun->disk_info.capacity * 512) / (1024 * 1024), 
		                           	 muxdLun->disk_info.capacity);
	DMSG_INFO("sector_size  = %d\n", muxdLun->disk_info.sector_size);
	DMSG_INFO("DevNo        = %d\n", MuxdDev->DevNo);
	DMSG_INFO("ClassName    = %s\n", MuxdDev->ClassName);
	DMSG_INFO("DevName      = %s\n", MuxdDev->DevName);
	DMSG_INFO("--------------------------------------------------\n");

	return ;
}



/*
*******************************************************************************
*                     UsbMuxdDevReg
*
* Description:
*    
*
* Parameters:
*    
* 
* Return value:
*    0	���ɹ�
*	!0	��ʧ��
*
* note:
*    ��
*
*******************************************************************************
*/
__s32 UsbMuxdDevReg(__UsbMuxdDev_t *MuxdDev, __u8 *ClassName, __u32 RegMuxd)
{
    if(MuxdDev == NULL || ClassName == NULL){
		DMSG_PANIC("ERR: UsbMuxdDevInit: input error\n");
		return -1;
	}

	//���±�־λ
	MuxdDev->Magic = USB_BLK_DEV_MAGIC;
	if(RegMuxd){
    	MuxdDev->is_RegMuxd = 1;
	}

	strncpy((char *)MuxdDev->ClassName, 
		           (const char *)ClassName, 
		           strlen((const char *)ClassName));

	/* save deivce info */
	memset(&MuxdDev->device_info, 0, sizeof(usbh_disk_device_info_t));
	strcpy((char *)MuxdDev->device_info.Classname, (char *)MuxdDev->ClassName);
	strcpy((char *)MuxdDev->device_info.DeviceName, (char *)MuxdDev->DevName);
	strcpy((char *)MuxdDev->device_info.DeivceInfo.Vender, (char *)MuxdDev->Lun->Vendor);
	strcpy((char *)MuxdDev->device_info.DeivceInfo.Product, (char *)MuxdDev->Lun->Product);
	strcpy((char *)MuxdDev->device_info.DeivceInfo.Serial, (char *)MuxdDev->Lun->Revision);
	MuxdDev->device_info.DeivceInfo.HubPortNo = (hub_GetHubNo(MuxdDev->Lun->muxdDev->pusb_dev) & 0x0f)
		                                       | (hub_GetHubSeries(MuxdDev->Lun->muxdDev->pusb_dev) & 0x0f) << 8;
	if((MuxdDev->Lun->DeviceType == MUXD_DEVICE_DIRECT_ACCESS) && (MuxdDev->Lun->RemoveAble == 0)){
		MuxdDev->device_info.DeivceInfo.DeviceType = USB_STORAGE_DEVICE_TYPE_HDD;
	}else if((MuxdDev->Lun->DeviceType == MUXD_DEVICE_DIRECT_ACCESS) && MuxdDev->Lun->RemoveAble){
		MuxdDev->device_info.DeivceInfo.DeviceType = USB_STORAGE_DEVICE_TYPE_UDISK;
	}else if(MuxdDev->Lun->DeviceType == MUXD_DEVICE_CDROM){
		MuxdDev->device_info.DeivceInfo.DeviceType = USB_STORAGE_DEVICE_TYPE_CDROM;
	}else{
		MuxdDev->device_info.DeivceInfo.DeviceType = USB_STORAGE_DEVICE_TYPE_UNKOWN;
	}

	DMSG_INFO("Classname  = %s\n", MuxdDev->device_info.Classname);
	DMSG_INFO("DeviceName = %s\n", MuxdDev->device_info.DeviceName);
	DMSG_INFO("Vender     = %s\n", MuxdDev->device_info.DeivceInfo.Vender);
	DMSG_INFO("Product    = %s\n", MuxdDev->device_info.DeivceInfo.Product);
	DMSG_INFO("Serial     = %s\n", MuxdDev->device_info.DeivceInfo.Serial);
	DMSG_INFO("HubPortNo  = %d\n", MuxdDev->device_info.DeivceInfo.HubPortNo);
	DMSG_INFO("DeviceType = %d\n", MuxdDev->device_info.DeivceInfo.DeviceType);

	//usbh_disk_SaveDeviceInfo(&MuxdDev->device_info);

	strcpy(MuxdDev->ClassName,"MUXD");
	strcpy(MuxdDev->DevName,"PHONE");
	
	/* reg disk */
	MuxdDev->DevRegHdle = esDEV_DevReg((const char *)MuxdDev->ClassName, (const char *)MuxdDev->DevName, &(MuxdDev->MuxdOp), (void *)MuxdDev);
	if(MuxdDev->DevRegHdle == NULL){
		DMSG_PANIC("ERR: Block device register failed.\n");
		MuxdDev->Magic = 0;
		MuxdDev->is_RegMuxd = 0;
		return USB_ERR_UNKOWN_ERROR;
	}

	DMSG_PANIC("\n..............................................................................\n");
	DMSG_PANIC("[USB Muxd]: Register new device, class = [%s], dev = [%s]\n", 
				MuxdDev->ClassName, MuxdDev->DevName);
	DMSG_PANIC("..............................................................................\n\n");
	
    return USB_ERR_SUCCESS;
}

/*
*******************************************************************************
*                     UsbMuxdDevUnReg
*
* Description:
*    
*
* Parameters:
*    
* 
* Return value:
*    0	���ɹ�
*	!0	��ʧ��
*
* note:
*    ��
*
*******************************************************************************
*/
__s32 UsbMuxdDevUnReg(__UsbMuxdDev_t *MuxdDev)
{
    if(MuxdDev == NULL){
		DMSG_PANIC("ERR: UsbMuxdDevUnReg: input error\n");
		return USB_ERR_BAD_ARGUMENTS;
	}

    if(MuxdDev->DevRegHdle){
		DMSG_INFO("\n..............................................................................\n");
		DMSG_INFO("[USB Muxd]: UnRegister old device, class = [%s], dev = [%s]\n", 
					MuxdDev->ClassName, MuxdDev->DevName);
		DMSG_INFO("..............................................................................\n\n");

		//usbh_disk_FreeDeviceInfo(&MuxdDev->device_info);
		esDEV_DevUnreg(MuxdDev->DevRegHdle);

		MuxdDev->DevRegHdle = NULL;
		MuxdDev->is_RegMuxd = 0;
		memset(MuxdDev->ClassName, 0, USB_BULK_DISK_MAX_NAME_LEN);
	}else{
	 	DMSG_PANIC("ERR: UsbMuxdDevUnReg: DevRegHdle = NULL\n");
	    return USB_ERR_BAD_ARGUMENTS;
	}

	//set_muxd_temp_buff_invalid_by_dev(MuxdDev->DevNo);
	MuxdDev->Magic = 0;

    return USB_ERR_SUCCESS;
}


