/*
********************************************************************************************************************
*                                              usb host driver
*
*                              (c) Copyright 2007-2010, javen.China
*										All	Rights Reserved
*
* File Name 	: UsbMuxdDev.h
*
* Author 		: javen
*
* Version 		: 2.0
*
* Date 			: 2010.03.02
*
* Description 	:
*
* History 		:
*
********************************************************************************************************************
*/
#ifndef  __MUXDDEV_H__
#define  __MUXDDEV_H__

#include  "usbh_disk_info.h"

#define USB_OS_HANDLE	__hdle /*epos的handle */

#define	 USB_BLK_DEV_MAGIC 				0x5a13d099
#define  USB_BULK_DISK_MAX_NAME_LEN		32

typedef struct __UsbMuxdDev{
    __u32   last_lun;               //���Ϊ1����ʾ�����һ������
	__u32	Magic;	             	/* ��ʾ�豸�Ƿ�Ϸ�					*/
	__muxdLun_t *Lun;          		/* sd������scsi device���е����� 	*/

    /* Muxd information */
	__u32 used;                     /* ���豸���� 					*/
	__dev_devop_t MuxdOp;			/* �豸�������� 					*/

    /* Muxd manager */
	USB_OS_HANDLE DevParaHdle;		/* openʱ�ľ��						*/
	USB_OS_HANDLE DevRegHdle;		/* regʱ�ľ�� 						*/

	__u32 DevNo;								/* ���豸��, ����host_id, target_id, lun ���	*/
    __u8 ClassName[USB_BULK_DISK_MAX_NAME_LEN];	/* �豸����, ��"disk" 				*/
    __u8 DevName[USB_BULK_DISK_MAX_NAME_LEN];	/* ���豸��, ��"SCSI_DISK_000"		*/

    __u32 is_RegMuxd;              	/* �Ƿ�ע���disk�豸 							*/
    __u32 ErrCmdNr;           		/* test_unit_ready�ڼ�, δ֪����Ĵ��� 			*/

	void *Extern;					/* ��չ����, ��cd 								*/

	usbh_disk_device_info_t device_info;
}__UsbMuxdDev_t;

//------------------------------------------
//
//------------------------------------------
__UsbMuxdDev_t * UsbMuxdDevAllocInit(__muxdLun_t * muxdLun);
__s32 UsbMuxdDevFree(__UsbMuxdDev_t *MuxdDev);

void GetMuxdInfo(__UsbMuxdDev_t *MuxdDev);
void ShutDown(__UsbMuxdDev_t *MuxdDev);

__s32 UsbMuxdDevReg(__UsbMuxdDev_t *MuxdDev, __u8 *ClassName, __u32 RegMuxd);
__s32 UsbMuxdDevUnReg(__UsbMuxdDev_t *MuxdDev);
#ifdef IOS_MIRROR_MFI
s32 muxd_ctrl_send(__muxdDev_t *muxdDev,__s32 lenght,char *data);
#endif


#endif   //__MUXDDEV_H__


