/*
* Copyright (c) 2019-2025 Allwinner Technology Co., Ltd. ALL rights reserved.
*
* Allwinner is a trademark of Allwinner Technology Co.,Ltd., registered in
* the the People's Republic of China and other countries.
* All Allwinner Technology Co.,Ltd. trademarks are used with permission.
*
* DISCLAIMER
* THIRD PARTY LICENCES MAY BE REQUIRED TO IMPLEMENT THE SOLUTION/PRODUCT.
* IF YOU NEED TO INTEGRATE THIRD PARTY’S TECHNOLOGY (SONY, DTS, DOLBY, AVS OR MPEGLA, ETC.)
* IN ALLWINNERS’SDK OR PRODUCTS, YOU SHALL BE SOLELY RESPONSIBLE TO OBTAIN
* ALL APPROPRIATELY REQUIRED THIRD PARTY LICENCES.
* ALLWINNER SHALL HAVE NO WARRANTY, INDEMNITY OR OTHER OBLIGATIONS WITH RESPECT TO MATTERS
* COVERED UNDER ANY REQUIRED THIRD PARTY LICENSE.
* YOU ARE SOLELY RESPONSIBLE FOR YOUR USAGE OF THIRD PARTY’S TECHNOLOGY.
*
*
* THIS SOFTWARE IS PROVIDED BY ALLWINNER"AS IS" AND TO THE MAXIMUM EXTENT
* PERMITTED BY LAW, ALLWINNER EXPRESSLY DISCLAIMS ALL WARRANTIES OF ANY KIND,
* WHETHER EXPRESS, IMPLIED OR STATUTORY, INCLUDING WITHOUT LIMITATION REGARDING
* THE TITLE, NON-INFRINGEMENT, ACCURACY, CONDITION, COMPLETENESS, PERFORMANCE
* OR MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
* IN NO EVENT SHALL ALLWINNER BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
* SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
* NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
* LOSS OF USE, DATA, OR PROFITS, OR BUSINESS INTERRUPTION)
* HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
* STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
* ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED
* OF THE POSSIBILITY OF SUCH DAMAGE.
*/
#ifndef _CIRCLE_BUF_H_
#define _CIRCLE_BUF_H_

typedef struct __AUDIO_DEV_BUF_MANAGER
{
    __u8        *pStart;        //buffer start address
    __u32       uTotalSize;     //buffer total size
    __u32       uBufSize;       //buffer use size, should be part or full of total size

    __u8        *pRdPtr;        //read pointer
    __u8        *pWrPtr;        //write pointer

    __u32       uDataSize;      //data size
    __u32       uFreeSize;      //free buffer size

} __audio_dev_buf_manager_t;


extern __s32 CircleBufCreate(__audio_dev_buf_manager_t *buf_par, __u32 size);
extern __s32 CircleBufDestroy(__audio_dev_buf_manager_t *buf_par);
extern __s32 CircleBufRead(__audio_dev_buf_manager_t *buf_par, __u8 *buf, __u32 size);
extern __s32 CircleBufReadZero(__audio_dev_buf_manager_t *buf_par, __u8 *buf, __u32 size);

extern __s32 CircleBufWrite(__audio_dev_buf_manager_t *buf_par, __u8 *buf, __u32 size);
extern __s32 CircleBufFlush(__audio_dev_buf_manager_t *buf_par);
extern __s32 CircleBufQuerySize(__audio_dev_buf_manager_t *buf_par, __u32 type);
extern __s32 CircleBufResize(__audio_dev_buf_manager_t *buf_par, __s32 size);


#endif /* _CIRCLE_BUF_I_H_ */