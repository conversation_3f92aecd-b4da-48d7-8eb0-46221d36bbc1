#include "cirbuf.h"
#include <log.h>



#define palloc(npage, mode)        	esMEMS_Palloc(npage, mode)
#define pfree(pblk, npage)            esMEMS_Pfree(pblk, npage)


__s32 RltCirBufCreate(struct BUF_MANAGER *buf_par, __u32 size)
{
	if(!buf_par)
		return EPDK_FAIL;
    //request buffer circle buffer
    buf_par->pStart = palloc((size + 1023) / 1024, 0);
    if(!buf_par->pStart)
    {
        __msg("Palloc buffer for circle buffer failed!\n");
        return EPDK_FAIL;
    }

    buf_par->uTotalSize = size;
    buf_par->uBufSize = size;
    buf_par->uDataSize = 0;
    buf_par->uFreeSize = buf_par->uBufSize;
    buf_par->pRdPtr = buf_par->pStart;
    buf_par->pWrPtr = buf_par->pStart;

    return EPDK_OK;
}


__s32 RltCirBufDestroy(struct BUF_MANAGER *buf_par)
{
    if(!buf_par)
		return EPDK_FAIL;
    if(buf_par->pStart && buf_par->uTotalSize)
    {
        pfree(buf_par->pStart, (buf_par->uTotalSize+1023)/1024);
    }

    memset(buf_par, 0, sizeof(struct BUF_MANAGER));

    return EPDK_OK;
}


__s32 RltCirBufRead(struct BUF_MANAGER *buf_par, __u8 *buf, __u32 size)
{
    __s32  result = 0;
    __u32  tmpSize0, tmpSize1;
    __u8    *tmpPtr0, *tmpPtr1;
    
    if(!buf_par || !buf || !size)
    {
        __msg("No data need be read!\n");
        return 0;
    }

    if(!buf_par->uDataSize)
    {
        __msg("No data in buffer!\n");
        return 0;
    }

    //calculate current cache data status
    if((buf_par->pRdPtr + buf_par->uDataSize) > (buf_par->pStart + buf_par->uBufSize))
    {
        tmpPtr0 = buf_par->pRdPtr;
        tmpSize0 = buf_par->pStart + buf_par->uBufSize - buf_par->pRdPtr;

        tmpPtr1 = buf_par->pStart;
        tmpSize1 = buf_par->uDataSize - tmpSize0;
    }
    else
    {
        tmpPtr0 = buf_par->pRdPtr;
        tmpSize0 = buf_par->uDataSize;

        tmpPtr1 = buf_par->pRdPtr + tmpSize0;
        tmpSize1 = 0;
    }

    if((tmpSize0 + tmpSize1) >= size)
    {
        //cache data is enough for read
        if(tmpSize0 >= size)
        {
            memcpy(buf, tmpPtr0, size);
        }
        else
        {
            memcpy(buf, tmpPtr0, tmpSize0);
            memcpy(buf+tmpSize0, tmpPtr1, size-tmpSize0);
        }

	  esKRNL_SchedLock();
	  
        //update buffer parameter
        buf_par->pRdPtr    += size;
        buf_par->uDataSize -= size;
        buf_par->uFreeSize += size;
        if(buf_par->pRdPtr >= buf_par->pStart + buf_par->uBufSize)
        {
            buf_par->pRdPtr -= buf_par->uBufSize;
        }

	  esKRNL_SchedUnlock();

        result = size;
    }
    else
    {
        //cache data is not enough for read
        memcpy(buf, tmpPtr0, tmpSize0);
        if(tmpSize1)
        {
            memcpy(buf+tmpSize0, tmpPtr1, tmpSize1);
        }

	  esKRNL_SchedLock();
        //update buffer parameter
        buf_par->pRdPtr    += tmpSize0+tmpSize1;
        buf_par->uDataSize -= tmpSize0+tmpSize1;
        buf_par->uFreeSize += tmpSize0+tmpSize1;
        if(buf_par->pRdPtr >= buf_par->pStart + buf_par->uBufSize)
        {
            buf_par->pRdPtr -= buf_par->uBufSize;
        }
	  esKRNL_SchedUnlock();

        result = tmpSize0+tmpSize1;
    }

    return result;
}

__s32 RltCirBufWrite(struct BUF_MANAGER *buf_par, __u8 *buf, __u32 size)
{
    __s32 result = 0;
    __u32 tmpSize0, tmpSize1;
    __u8  *tmpPtr0, *tmpPtr1;

    if(!buf_par || !buf || !size)
    {
        __msg("No data need be write!\n");
        return 0;
    }
 
    if(!buf_par->uFreeSize)
    {
        __msg("No free buffer for store data!\n");
        return 0;
    }

    //calculate current free buffer status
    if((buf_par->pWrPtr + buf_par->uFreeSize) > (buf_par->pStart + buf_par->uBufSize))
    {
        //free buffer is two segment
        tmpPtr0 = buf_par->pWrPtr;
        tmpSize0 = buf_par->pStart + buf_par->uBufSize - buf_par->pWrPtr;

        tmpPtr1 = buf_par->pStart;
        tmpSize1 = buf_par->uFreeSize - tmpSize0;
   }
    else
    {
        tmpPtr0 = buf_par->pWrPtr;
        tmpSize0 = buf_par->uFreeSize;

        tmpPtr1 = buf_par->pWrPtr + tmpSize0;
        tmpSize1 = 0;
    }

    if((tmpSize0 + tmpSize1) >= size)
    {
        //free buffer is enough for write
        if(tmpSize0 >= size)
        {
            memcpy(tmpPtr0, buf, size);
        }
        else
        {
            memcpy(tmpPtr0, buf, tmpSize0);
            memcpy(tmpPtr1, buf+tmpSize0, size-tmpSize0);
        }

	  esKRNL_SchedLock();
        //update buffer parameter
        buf_par->pWrPtr    += size;
        buf_par->uFreeSize -= size;
        buf_par->uDataSize += size;
        if(buf_par->pWrPtr >= buf_par->pStart + buf_par->uBufSize)
        {
            buf_par->pWrPtr -= buf_par->uBufSize;
        }
	  esKRNL_SchedUnlock();

        result = size;
    }
    else
    {
        //cache data is not enough for read
        memcpy(tmpPtr0, buf, tmpSize0);
        if(tmpSize1)
        {
            memcpy(tmpPtr1, buf+tmpSize0, tmpSize1);
        }

	  esKRNL_SchedLock();
        //update buffer parameter
        buf_par->pWrPtr    += tmpSize0+tmpSize1;
        buf_par->uDataSize += tmpSize0+tmpSize1;
        buf_par->uFreeSize -= tmpSize0+tmpSize1;
        if(buf_par->pWrPtr >= buf_par->pStart + buf_par->uBufSize)
        {
            buf_par->pWrPtr -= buf_par->uBufSize;
        }
	  esKRNL_SchedUnlock();

        result = tmpSize0+tmpSize1;
    }

    return result;
}

__s32 RltCirBufFlush(struct BUF_MANAGER *buf_par)
{
	if(buf_par == NULL)
	{
		printk("error buf_par == NULL\n");
		return 0;
	}

    buf_par->pRdPtr  = buf_par->pWrPtr;
    buf_par->uDataSize = 0;
    buf_par->uFreeSize = buf_par->uBufSize;

    return EPDK_OK;
}

__s32 RltCirBufQuery(struct BUF_MANAGER *buf_par, __u32 type)
{
	if(buf_par == NULL)
	{
		printk("error buf_par == NULL\n");
		return 0;
	}


    switch(type)
    {
        case QUERY_BUF_SIZE_DATA:
            return buf_par->uDataSize;

        case QUERY_BUF_SIZE_FREE:
            return buf_par->uFreeSize;

        case QUERY_BUF_SIZE_TOTAL:
            return buf_par->uBufSize;

        default:
            return -1;
    }
}


