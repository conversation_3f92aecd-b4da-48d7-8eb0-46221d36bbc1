

#ifndef _CIRBUF_H_
#define _CIRBUF_H_

#include <typedef.h>
#include <kapi.h>
#include <elibs_stdio.h>
#include <elibs_string.h>

typedef struct BUF_MANAGER
{
    __u8        *pStart;        //buffer start address
    __u32       uTotalSize;     //buffer total size
    __u32       uBufSize;       //buffer use size, should be part or full of total size

    __u8        *pRdPtr;        //read pointer
    __u8        *pWrPtr;        //write pointer

    __u32       uDataSize;      //data size
    __u32       uFreeSize;      //free buffer size

};

typedef enum QUERY_BUF_SIZE_TYPE
{
    QUERY_BUF_SIZE_NONE = 0,      /* δ����Ҫ��ȡ�ռ������           */
    QUERY_BUF_SIZE_DATA,          /* ��ȡ�������ڵ�����size           */
    QUERY_BUF_SIZE_FREE,          /* ��ȡ�������ڵĿ��пռ�size       */
    QUERY_BUF_SIZE_TOTAL,         /* ��ȡ�������ܿռ�                 */

};


extern __s32 RltCirBufCreate(struct BUF_MANAGER *buf_par, __u32 size);
extern __s32 RltCirBufDestroy(struct BUF_MANAGER *buf_par);
extern __s32 RltCirBufRead(struct BUF_MANAGER *buf_par, __u8 *buf, __u32 size);
extern __s32 RltCirBufWrite(struct BUF_MANAGER *buf_par, __u8 *buf, __u32 size);
extern __s32 RltCirBufQuery(struct BUF_MANAGER *buf_par, __u32 type);
extern __s32 RltCirBufFlush(struct BUF_MANAGER *buf_par);
#endif /* _CIRBUF_I_H_ */

